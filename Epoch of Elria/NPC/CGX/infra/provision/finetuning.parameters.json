{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#", "contentVersion": "*******", "parameters": {"defaultCommands": {"value": ["cd /mount", "pip install huggingface-hub==0.22.2", "huggingface-cli download meta-llama/Meta-Llama-3-8B --revision main --local-dir ./model-cache/meta-llama/Llama-v3-8b --local-dir-use-symlinks False --cache-dir ./cache/hfdownload", "pip install -r ./setup/requirements.txt", "python3 ./finetuning/invoke_olive.py && find models/ -print | grep adapter/adapter"]}, "maximumInstanceCount": {"value": 2}, "timeout": {"value": 10800}, "location": {"value": null}, "storageAccountName": {"value": null}, "fileShareName": {"value": null}, "acaEnvironmentName": {"value": null}, "acaEnvironmentStorageName": {"value": null}, "acaJobName": {"value": null}, "acaLogAnalyticsName": {"value": null}}}