{
    "input_model": {
        "type": "HfModel",
        "model_path": "model-cache/meta-llama/Llama-v3-8b",
        "load_kwargs": {
            "trust_remote_code": true
        }
    },
    "systems": {
        "local_system": {
            "type": "LocalSystem",
            "accelerators": [
                {
                    "device": "gpu",
                    "execution_providers": [
                        "CUDAExecutionProvider"
                    ]
                }
            ]
        },
        "target_system": {
            "type": "LocalSystem",
            "accelerators": [
                {
                    "device": "gpu",
                    "execution_providers": [
                        "CUDAExecutionProvider"
                    ]
                }
            ]
        }
    },
    "data_configs": [
        {
            "name": "dataset_default_train",
            "type": "HuggingfaceContainer",
            "user_script": "finetuning/qlora_user_script.py",
            "load_dataset_config": {
#data_configs_data_files_extension_start
            <!--    "data_name": "<data_configs_data_files_extension>", -->
#data_configs_data_files_extension_end
                "<data_files>": "<data_configs_data_files>",
                "split": "<data_configs_split>"
            },
            "pre_process_data_config": {
                "dataset_type": "<dataset_type>",
                "text_cols": <text_cols>,
                "text_template": "<text_template>",
                "corpus_strategy": "<line_by_line>",
                "source_max_len": <source_max_len>,
                "pad_to_max_len": <pad_to_max_len>,
                "use_attention_mask": false
            }
        }
    ],
    "passes": {
        "qlora": {
            "type": "QLoRA",
            "compute_dtype": "<compute_dtype>",
            "quant_type": "<quant_type>",
            "double_quant": <double_quant>,
            "lora_r": <lora_r>,
            "lora_alpha": <lora_alpha>,
            "lora_dropout": <lora_dropout>,
            "train_data_config": "dataset_default_train",
            "training_args": {
                "seed": <training_args_seed>,
                "data_seed": <training_args_data_seed>,
                "per_device_train_batch_size": <per_device_train_batch_size>,
                "per_device_eval_batch_size": <per_device_eval_batch_size>,
                "gradient_accumulation_steps": <gradient_accumulation_steps>,
                "gradient_checkpointing": <gradient_checkpointing>,
                "learning_rate": <learning_rate>,
                "num_train_epochs":<num_train_epochs>,
                "max_steps": <max_steps>,
                "logging_steps": 10,
                "evaluation_strategy": "steps",
                "eval_steps": 187,
                "group_by_length": true,
                "adam_beta2": 0.999,
                "max_grad_norm": 0.3,
                "output_dir": "<output_dir>"
            }
        },
        "OnnxConversion": {
            "type": "OnnxConversion",
            "target_opset": 17,
            "torch_dtype": "float32",
            "save_metadata_for_token_generation": true
        },
        "OrtTransformersOptimization": {
            "type": "OrtTransformersOptimization",
            "model_type": "gpt2",
            "opt_level": 0,
            "keep_io_types": false,
            "float16": true
        },
        "ExtractAdapters": {
            "type": "ExtractAdapters",
            "make_inputs": true,
            "pack_inputs": true
        },
        "ModelBuilder": {
            "type": "ModelBuilder",
            "metadata_only": true,
            "precision": "fp16"
        }
    },
    "log_severity_level": 0,
    "search_strategy": false,
    "evaluate_input_model": false,
    "host": "local_system",
    "target": "target_system",
    "cache_dir": "cache",
    "output_dir": "models/qlora"
}